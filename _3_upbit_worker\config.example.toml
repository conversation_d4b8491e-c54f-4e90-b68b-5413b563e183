# Upbit Worker 多机器HTTP发送配置示例
# 这个配置文件展示如何配置_3_upbit_worker并发发送到多台_4_trading_engine机器

[upbit]
# Upbit API 基础 URL
api_base_url = "https://api-manager.upbit.com/api/v1/announcements"

# 请求超时时间（秒）
timeout_seconds = 30

# 请求重试次数
max_retries = 3

# 请求间隔（毫秒）
request_interval_ms = 5000

[watcher]
# 监听间隔（秒）
# 建议值：
# - 30-60秒：安全且及时
# - 10-30秒：中等风险
# - 5-10秒：需要代理
poll_interval_seconds = 30

# 基线公告数量（用于初始化）
baseline_count = 30

# 预留最新公告数量（用于触发检测）
reserved_count = 3

# 每次获取的公告数量
fetch_count = 3

# 状态文件路径
state_file_path = "upbit_worker_state.json"

# 首次运行配置（当状态文件不存在时使用）
[watcher.first_run]
# 首次获取的公告总数
total_fetch_count = 120
# 首次预留的公告数量（用于测试）
reserved_count = 90

[logging]
# 日志级别: trace, debug, info, warn, error
level = "info"

# 是否启用 JSON 格式日志
json_format = false

# 日志文件路径（可选，留空则只输出到控制台）
# file_path = "logs/upbit_worker.log"

[output]
# 输出格式: json, text
format = "json"

# 输出目标: stdout, file, http
target = "http"

# 输出文件路径（当 target 为 file 时）
# file_path = "output/notices.jsonl"

# HTTP 端点（当 target 为 http 时）
# http_endpoint = "http://localhost:8080/api/notices"

# 多个HTTP端点列表（用于并发发送到多台机器）
# 当 target = "http" 时，会并发发送到以下所有端点
http_endpoints = [
    "http://*************:8080/api/notices",  # 机器1 - _4_trading_engine
    "http://*************:8080/api/notices",  # 机器2 - _4_trading_engine
    "http://*************:8080/api/notices",  # 机器3 - _4_trading_engine
    "http://*************:8080/api/notices",  # 机器4 - _4_trading_engine
]

# HTTP请求超时时间（秒）
http_timeout_seconds = 5

[output.simplified_json]
# 简化JSON文件输出配置
# 只包含timestamp和symbol两个字段，用于本地存储
enabled = true
file_path = "upbit_symbols.json"

# 使用说明：
# 1. 将此文件重命名为 config.toml 或使用 --config 参数指定
# 2. 修改 http_endpoints 中的IP地址为实际的_4_trading_engine机器地址
# 3. 确保所有_4_trading_engine机器都在端口8080上运行并监听/api/notices端点
# 4. 运行: cargo run --release
# 
# 生产环境部署建议：
# - 多个_3_upbit_worker实例可以配置不同的http_endpoints进行负载分散
# - 每个_4_trading_engine机器应该配置不同的交易策略和风险参数
# - 使用负载均衡器可以进一步优化分发策略
