# Upbit Worker 配置文件
# 这是 Upbit 数据获取服务的配置文件

[upbit]
# Upbit API 基础 URL
api_base_url = "https://api-manager.upbit.com/api/v1/announcements"

# 请求超时时间（秒）
timeout_seconds = 30

# 请求重试次数
max_retries = 3

# 请求间隔（毫秒）
request_interval_ms = 5000

[watcher]
# 监听间隔（秒）
# 建议值：
# - 30-60秒：安全且及时
# - 10-30秒：中等风险
# - 5-10秒：需要代理
poll_interval_seconds = 30

# 基线公告数量（用于初始化）
baseline_count = 30

# 预留最新公告数量（用于触发检测）
reserved_count = 3

# 每次获取的公告数量
fetch_count = 3

# 状态文件路径
state_file_path = "upbit_worker_state.json"

# 首次运行配置（当状态文件不存在时使用）
[watcher.first_run]
# 首次获取的公告总数
total_fetch_count = 20
# 首次预留的公告数量（用于测试）
reserved_count = 10

[logging]
# 日志级别: trace, debug, info, warn, error
level = "info"

# 是否启用 JSON 格式日志
json_format = false

# 日志文件路径（可选，留空则只输出到控制台）
# file_path = "logs/upbit_worker.log"

[output]
# 输出格式: json, text
format = "json"

# 输出目标: stdout, file, http
# target = "stdout"
target = "file"
# target = "http"

# 输出文件路径（当 target 为 file 时）
# file_path = "output/notices.jsonl"

# HTTP 端点（当 target 为 http 时）
# http_endpoint = "http://localhost:8080/api/notices"

# 多个HTTP端点列表（用于并发发送到多台机器）
# 当 target = "http" 时，会并发发送到以下所有端点
http_endpoints = [
    # "http://*************:8080/api/notices",  # 机器1
    # "http://*************:8080/api/notices",  # 机器2
    # "http://*************:8080/api/notices",  # 机器3
]

# HTTP请求超时时间（秒）
http_timeout_seconds = 5

[output.simplified_json]
# 简化JSON文件输出配置
# 只包含timestamp和symbol两个字段，用于本地存储
enabled = true
file_path = "upbit_symbols.json"


